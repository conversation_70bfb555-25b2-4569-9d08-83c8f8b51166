"use client";

import { useEffect, useState, ReactNode } from "react";
import { useRouter } from "next/navigation";
import { useFrappeAuth } from "frappe-react-sdk";

interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
  redirectTo?: string;
}

export function AuthGuard({ 
  children, 
  fallback = <AuthLoadingScreen />, 
  redirectTo = "/login" 
}: AuthGuardProps) {
  const router = useRouter();
  const { currentUser, isValidating } = useFrappeAuth();
  const [isChecking, setIsChecking] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const checkAuth = () => {
      console.log("AuthGuard - Checking auth:", { currentUser, isValidating });

      if (!isValidating) {
        if (currentUser) {
          setIsAuthenticated(true);
          setIsChecking(false);
        } else {
          // Kısa bir delay ile redirect - false positive'leri <PERSON>n<PERSON> için
          timeoutId = setTimeout(() => {
            console.log("AuthGuard - No user found, redirecting to:", redirectTo);
            setIsAuthenticated(false);
            setIsChecking(false);
            router.replace(redirectTo);
          }, 500);
        }
      }
    };

    checkAuth();

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [currentUser, isValidating, router, redirectTo]);

  // Loading state
  if (isChecking || isValidating) {
    return fallback;
  }

  // Not authenticated
  if (!isAuthenticated || !currentUser) {
    return null;
  }

  // Authenticated
  return <>{children}</>;
}

function AuthLoadingScreen() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">Verifying authentication...</p>
      </div>
    </div>
  );
}
