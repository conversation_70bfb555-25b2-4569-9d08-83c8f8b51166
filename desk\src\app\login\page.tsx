"use client";

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useFrappeAuth } from "frappe-react-sdk"
import { GalleryVerticalEnd } from "lucide-react"
import { LoginForm } from "@/components/form/login-form"

export default function LoginPage() {
  const router = useRouter()
  const { currentUser, isValidating } = useFrappeAuth()

  // Eğer zaten login olmuşsa dashboard'a yönlendir
  useEffect(() => {
    if (!isValidating && currentUser) {
      console.log("Already logged in, redirecting to dashboard")
      router.push("/dashboard")
    }
  }, [currentUser, isValidating, router])

  // Loading state
  if (isValidating) {
    return (
      <div className="relative flex items-center justify-center min-h-svh bg-slate-800">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
          <p className="mt-2">Loading...</p>
        </div>
      </div>
    )
  }

  // Eğer zaten login olmuşsa loading göster (redirect olana kadar)
  if (currentUser) {
    return (
      <div className="relative flex items-center justify-center min-h-svh bg-slate-800">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
          <p className="mt-2">Redirecting...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="relative flex items-center justify-center min-h-svh bg-slate-800">
      {/* Background gradient */}
      <div className="absolute inset-0 -z-10 bg-gradient-to-br from-slate-900 to-slate-700">
        <div className="absolute inset-0 opacity-20">
          <img
            src="/images/backgraund.jpg"
            alt="Background"
            className="h-full w-full object-cover"
          />
        </div>
      </div>

      {/* Logo in the top left */}
      <div className="absolute top-8 left-8">
        <a href="#" className="flex items-center gap-2 font-bold text-white text-xl">
          <div className="bg-teal-500 text-white flex size-10 items-center justify-center rounded-md shadow-lg">
            <GalleryVerticalEnd className="size-6" />
          </div>
          <span className="drop-shadow-md text-xl">SEBS IOT</span>
        </a>
      </div>

      {/* Centered login form card */}
      <div className="w-full max-w-md mx-4">
        <div className="bg-white rounded-lg p-8 shadow-xl">
          <LoginForm />
          <div className="mt-6 text-center text-sm text-gray-500">
            <p>© 2025 SEBS IOT Management System</p>
          </div>
        </div>
      </div>
    </div>
  )
}