"use client";

import { useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { useFrappeAuth } from "frappe-react-sdk"
import { Shield, Zap, Users, BarChart3 } from "lucide-react"
import { LoginForm } from "@/components/form/login-form"

export default function LoginPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { currentUser, isValidating } = useFrappeAuth()
  const callbackUrl = searchParams.get('callbackUrl') || '/dashboard'

  // Eğer zaten login olmuşsa callback URL'e yönlendir
  useEffect(() => {
    if (!isValidating && currentUser) {
      console.log("Already logged in, redirecting to:", callbackUrl)
      router.replace(callbackUrl) // replace kullan - back button ile geri gelmesin
    }
  }, [currentUser, isValidating, router, callbackUrl])

  // Loading state - authentication kontrol ediliyor
  if (isValidating) {
    return (
      <div className="relative flex items-center justify-center min-h-svh bg-slate-800">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
          <p className="mt-2">Checking authentication...</p>
        </div>
      </div>
    )
  }

  // Eğer zaten login olmuşsa loading göster (redirect olana kadar)
  if (currentUser) {
    return (
      <div className="relative flex items-center justify-center min-h-svh bg-slate-800">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
          <p className="mt-2">Redirecting to dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="grid min-h-screen lg:grid-cols-2">
        {/* Sol Panel - Login Form */}
        <div className="flex flex-col justify-center px-6 py-12 lg:px-8">
          <div className="mx-auto w-full max-w-md">
            {/* Logo ve Başlık */}
            <div className="text-center mb-8">
              <div className="flex justify-center mb-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg">
                  <Shield className="h-6 w-6" />
                </div>
              </div>
              <h1 className="text-3xl font-bold tracking-tight text-gray-900 dark:text-white">
                IoT Gateway
              </h1>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                Güvenli ve akıllı cihaz yönetimi platformu
              </p>
            </div>

            {/* Login Form */}
            <div className="bg-white dark:bg-slate-800 rounded-2xl shadow-xl p-8 border border-gray-200 dark:border-slate-700">
              <LoginForm />
            </div>

            {/* Alt Bilgi */}
            <p className="mt-8 text-center text-xs text-gray-500 dark:text-gray-400">
              © 2024 IoT Gateway. Tüm hakları saklıdır.
            </p>
          </div>
        </div>

        {/* Sağ Panel - Feature Showcase */}
        <div className="hidden lg:flex lg:flex-col lg:justify-center lg:px-12 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"2\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>

          <div className="relative z-10 text-white">
            <h2 className="text-4xl font-bold mb-6">
              IoT Cihazlarınızı
              <br />
              <span className="text-blue-200">Akıllıca Yönetin</span>
            </h2>

            <p className="text-xl text-blue-100 mb-12 leading-relaxed">
              Gelişmiş analitik, gerçek zamanlı izleme ve güvenli bağlantı ile
              IoT ekosisteminizi tam kontrol altına alın.
            </p>

            {/* Features */}
            <div className="space-y-6">
              <div className="flex items-center space-x-4">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-white/10 backdrop-blur-sm">
                  <Zap className="h-5 w-5 text-yellow-300" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">Gerçek Zamanlı İzleme</h3>
                  <p className="text-blue-100 text-sm">Cihazlarınızı anlık olarak takip edin</p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-white/10 backdrop-blur-sm">
                  <BarChart3 className="h-5 w-5 text-green-300" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">Gelişmiş Analitik</h3>
                  <p className="text-blue-100 text-sm">Verilerinizi anlamlı içgörülere dönüştürün</p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-white/10 backdrop-blur-sm">
                  <Users className="h-5 w-5 text-purple-300" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">Ekip Yönetimi</h3>
                  <p className="text-blue-100 text-sm">Kullanıcı rolleri ve izinleri</p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-white/10 backdrop-blur-sm">
                  <Shield className="h-5 w-5 text-blue-300" />
                </div>
                <div>
                  <h3 className="font-semibold text-white">Kurumsal Güvenlik</h3>
                  <p className="text-blue-100 text-sm">End-to-end şifreleme ve güvenlik</p>
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="mt-12 grid grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">10K+</div>
                <div className="text-sm text-blue-200">Aktif Cihaz</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">99.9%</div>
                <div className="text-sm text-blue-200">Uptime</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">24/7</div>
                <div className="text-sm text-blue-200">Destek</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}