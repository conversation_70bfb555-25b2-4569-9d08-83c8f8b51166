"use client";

import { getSession } from "next-auth/react";

// Frappe API istekleri için cookie interceptor
export class FrappeInterceptor {
  private static instance: FrappeInterceptor;
  private frappeCookies: string | null = null;

  private constructor() {}

  public static getInstance(): FrappeInterceptor {
    if (!FrappeInterceptor.instance) {
      FrappeInterceptor.instance = new FrappeInterceptor();
    }
    return FrappeInterceptor.instance;
  }

  // Session'dan Frappe cookie'lerini al
  public async updateCookiesFromSession() {
    try {
      const session = await getSession();
      if (session?.frappeCookies) {
        this.frappeCookies = session.frappeCookies;
      }
    } catch (error) {
      console.warn('Could not get session cookies:', error);
    }
  }

  // Frappe API isteği gönder
  public async makeRequest(url: string, options: RequestInit = {}) {
    // Cookie'leri güncelle
    await this.updateCookiesFromSession();

    const headers = new Headers(options.headers);
    
    // Frappe cookie'lerini ekle
    if (this.frappeCookies) {
      headers.set('Cookie', this.frappeCookies);
    }

    // CSRF token'ı ekle (gerekirse)
    headers.set('X-Frappe-CSRF-Token', 'no-csrf');

    const requestOptions: RequestInit = {
      ...options,
      headers,
      credentials: 'include'
    };

    try {
      const response = await fetch(url, requestOptions);
      
      // Yeni cookie'ler varsa güncelle
      const setCookieHeader = response.headers.get('set-cookie');
      if (setCookieHeader) {
        this.frappeCookies = setCookieHeader;
      }

      return response;
    } catch (error) {
      console.error('Frappe request failed:', error);
      throw error;
    }
  }

  // GET isteği
  public async get(endpoint: string) {
    const url = `http://localhost:8000${endpoint}`;
    return this.makeRequest(url, { method: 'GET' });
  }

  // POST isteği
  public async post(endpoint: string, data?: any) {
    const url = `http://localhost:8000${endpoint}`;
    return this.makeRequest(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: data ? JSON.stringify(data) : undefined
    });
  }

  // PUT isteği
  public async put(endpoint: string, data?: any) {
    const url = `http://localhost:8000${endpoint}`;
    return this.makeRequest(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: data ? JSON.stringify(data) : undefined
    });
  }

  // DELETE isteği
  public async delete(endpoint: string) {
    const url = `http://localhost:8000${endpoint}`;
    return this.makeRequest(url, { method: 'DELETE' });
  }
}

// Singleton instance'ı export et
export const frappeInterceptor = FrappeInterceptor.getInstance();
