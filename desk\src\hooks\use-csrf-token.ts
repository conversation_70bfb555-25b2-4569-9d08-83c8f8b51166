"use client";

import { useEffect, useCallback, useState } from "react";
import { useFrappeAuth } from "frappe-react-sdk";

export function useCSRFToken() {
  const { currentUser } = useFrappeAuth();
  const [csrfToken, setCsrfToken] = useState<string | null>(null);

  // CSRF token'ı window objesine set et
  const setCSRFToken = useCallback((token: string) => {
    if (typeof window !== 'undefined') {
      window.csrf_token = token;
      setCsrfToken(token);
      // Sessiz - console log kaldırıldı
    }
  }, []);

  // CSRF token'ı cookie'den al
  const getCSRFFromCookie = useCallback(() => {
    if (typeof window !== 'undefined') {
      const cookies = document.cookie.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=');
        acc[key] = decodeURIComponent(value || '');
        return acc;
      }, {} as Record<string, string>);

      return cookies.csrf_token || cookies['csrf-token'] || null;
    }
    return null;
  }, []);

  // CSRF token'ı başlat
  useEffect(() => {
    if (!currentUser) return;

    // Cookie'den CSRF token'ı al
    const cookieToken = getCSRFFromCookie();
    if (cookieToken && cookieToken !== '{{ csrf_token }}') {
      setCSRFToken(cookieToken);
    } else {
      // Eğer cookie'de yoksa, basit bir token oluştur
      const simpleToken = Math.random().toString(36).substring(2, 15) +
                         Math.random().toString(36).substring(2, 15);
      setCSRFToken(simpleToken);
    }
  }, [currentUser, setCSRFToken, getCSRFFromCookie]);

  // CSRF token'ı manuel olarak yenile
  const refreshCSRFToken = useCallback(() => {
    const newToken = Math.random().toString(36).substring(2, 15) +
                     Math.random().toString(36).substring(2, 15);
    setCSRFToken(newToken);
  }, [setCSRFToken]);

  return {
    csrfToken,
    refreshCSRFToken,
    setCSRFToken
  };
}
