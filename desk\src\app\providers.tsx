"use client";

import { ReactNode } from "react";
import { Toaster } from "sonner";
import { FrappeProvider } from "frappe-react-sdk";
import { getFrappeURL } from "@/lib/frappe/frappeClient";

export function Providers({ children }: { children: ReactNode }) {
  return (
    <FrappeProvider
      url={getFrappeURL()}
      socketPort="9000"
      enableSocket={false}
    >
      {children}
      <Toaster position="top-right" />
    </FrappeProvider>
  );
}