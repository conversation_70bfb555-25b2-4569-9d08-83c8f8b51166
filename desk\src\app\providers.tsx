"use client";

import { ReactNode, useEffect, useState } from "react";
import { SessionProvider } from "next-auth/react";
import { Toaster } from "sonner";
import { FrappeProvider } from "frappe-react-sdk";

export function Providers({ children }: { children: ReactNode }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <SessionProvider>
        {children}
        <Toaster position="top-right" />
      </SessionProvider>
    );
  }

  return (
    <SessionProvider>
      <FrappeProvider
        url="http://localhost:8000"
        socketPort="9000"
        enableSocket={false}
      >
        {children}
        <Toaster position="top-right" />
      </FrappeProvider>
    </SessionProvider>
  );
}