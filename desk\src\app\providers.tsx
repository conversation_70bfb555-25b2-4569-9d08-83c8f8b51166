"use client";

import { ReactNode } from "react";
import { SessionProvider } from "next-auth/react";
import { Toaster } from "sonner";
import { FrappeProvider } from "frappe-react-sdk";
import { getFrappeURL } from "@/lib/frappe/frappeClient";

export function Providers({ children }: { children: ReactNode }) {
  return (
    <SessionProvider>
      <FrappeProvider
        url={getFrappeURL()}
        socketPort="9000"
        enableSocket={false}
      >
        {children}
        <Toaster position="top-right" />
      </FrappeProvider>
    </SessionProvider>
  );
}