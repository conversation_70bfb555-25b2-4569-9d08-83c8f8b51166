"use client";

import { ReactNode } from "react";
import { Toaster } from "sonner";
import { FrappeProvider } from "frappe-react-sdk";
import { getFrappeURL } from "@/lib/frappe/frappeClient";

export function Providers({ children }: { children: ReactNode }) {
  return (
    <FrappeProvider
      url={getFrappeURL()}
      socketPort="9000"
      tokenParams={{
        type: "Bearer",
        useToken: false // Cookie-based auth için false
      }}
    >
      {children}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: 'white',
            border: '1px solid #e5e7eb',
            color: '#374151'
          }
        }}
      />
    </FrappeProvider>
  );
}