"use client";

import { ReactNode } from "react";
import { SessionProvider } from "next-auth/react";
import { Toaster } from "sonner";
import { FrappeProviderWrapper } from "@/components/providers/frappe-provider-wrapper";

export function Providers({ children }: { children: ReactNode }) {
  return (
    <SessionProvider>
      <FrappeProviderWrapper>
        {children}
        <Toaster position="top-right" />
      </FrappeProviderWrapper>
    </SessionProvider>
  );
}