"use client";

import { useMemo } from "react";
import { useFrappeAuth } from "frappe-react-sdk";

interface UserInfo {
  name: string;
  full_name: string;
  email: string;
}

export function useUserInfo(): UserInfo {
  const { currentUser } = useFrappeAuth();

  return useMemo(() => {
    if (!currentUser) {
      return {
        name: '',
        full_name: '',
        email: ''
      };
    }

    // <PERSON><PERSON>'lerden bilgi al
    if (typeof window !== 'undefined') {
      const cookies = document.cookie.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=');
        acc[key] = decodeURIComponent(value || '');
        return acc;
      }, {} as Record<string, string>);

      return {
        name: currentUser,
        full_name: cookies.full_name || currentUser,
        email: cookies.user_id || currentUser
      };
    }

    return {
      name: currentUser,
      full_name: currentUser,
      email: currentUser
    };
  }, [currentUser]);
}
