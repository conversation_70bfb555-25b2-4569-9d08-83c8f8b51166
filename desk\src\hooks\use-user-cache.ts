"use client";

import { useState, useEffect, useCallback } from "react";
import { useFrappeA<PERSON>, useFrappePostCall } from "frappe-react-sdk";

interface UserProfile {
  name: string;
  full_name: string;
  email: string;
  user_image?: string;
  roles: string[];
  permissions: Record<string, any>;
  preferences: Record<string, any>;
  last_login?: string;
  creation?: string;
}

interface CacheConfig {
  ttl?: number; // Time to live in ms - default 5 minutes
  refreshInterval?: number; // Auto refresh interval in ms - default 2 minutes
}

const CACHE_KEY = 'frappe_user_profile';
const CACHE_TIMESTAMP_KEY = 'frappe_user_profile_timestamp';

export function useUserCache(config: CacheConfig = {}) {
  const { currentUser } = useFrappeAuth();
  const { call } = useFrappePostCall();

  const {
    ttl = 5 * 60 * 1000, // 5 minutes
    refreshInterval = 2 * 60 * 1000 // 2 minutes
  } = config;

  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get cached data
  const getCachedProfile = useCallback((): UserProfile | null => {
    if (typeof window === 'undefined') return null;

    try {
      const cached = localStorage.getItem(CACHE_KEY);
      const timestamp = localStorage.getItem(CACHE_TIMESTAMP_KEY);

      if (!cached || !timestamp) return null;

      const age = Date.now() - parseInt(timestamp);
      if (age > ttl) {
        // Cache expired
        localStorage.removeItem(CACHE_KEY);
        localStorage.removeItem(CACHE_TIMESTAMP_KEY);
        return null;
      }

      return JSON.parse(cached);
    } catch (error) {
      console.error("Error reading user cache:", error);
      return null;
    }
  }, [ttl]);

  // Set cached data
  const setCachedProfile = useCallback((profile: UserProfile) => {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(CACHE_KEY, JSON.stringify(profile));
      localStorage.setItem(CACHE_TIMESTAMP_KEY, Date.now().toString());
    } catch (error) {
      console.error("Error setting user cache:", error);
    }
  }, []);

  // Clear cached data
  const clearCache = useCallback(() => {
    if (typeof window === 'undefined') return;

    localStorage.removeItem(CACHE_KEY);
    localStorage.removeItem(CACHE_TIMESTAMP_KEY);
    setUserProfile(null);
  }, []);

  // Fetch user profile from server
  const fetchUserProfile = useCallback(async (force = false): Promise<UserProfile | null> => {
    if (!currentUser) return null;

    // Check cache first (unless forced)
    if (!force) {
      const cached = getCachedProfile();
      if (cached) {
        setUserProfile(cached);
        return cached;
      }
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log("Fetching user profile for:", currentUser);

      // Create basic profile first
      let profile: UserProfile = {
        name: currentUser,
        full_name: currentUser,
        email: '',
        user_image: '',
        roles: [],
        permissions: {},
        preferences: {},
        last_login: '',
        creation: ''
      };

      // Sadece cookie'lerden bilgi al - API çağrısı yapma
      // Frappe cookie'lerinden bilgileri çıkar
      if (typeof window !== 'undefined') {
        const cookies = document.cookie.split(';').reduce((acc, cookie) => {
          const [key, value] = cookie.trim().split('=');
          acc[key] = decodeURIComponent(value || '');
          return acc;
        }, {} as Record<string, string>);

        console.log("Available cookies:", cookies);

        // Cookie'lerden bilgileri al
        profile.full_name = cookies.full_name || currentUser;
        profile.email = cookies.user_id || '';

        // Eğer full_name cookie'si yoksa, currentUser'ı kullan
        if (!profile.full_name || profile.full_name === 'undefined') {
          profile.full_name = currentUser;
        }
      }

      console.log("Final user profile:", profile);

      setUserProfile(profile);
      setCachedProfile(profile);

      return profile;
    } catch (error: any) {
      console.error("Critical error in fetchUserProfile:", error);

      // Even if there's an error, create a minimal profile
      const fallbackProfile: UserProfile = {
        name: currentUser,
        full_name: currentUser,
        email: '',
        user_image: '',
        roles: [],
        permissions: {},
        preferences: {},
        last_login: '',
        creation: ''
      };

      setUserProfile(fallbackProfile);
      setError("Could not fetch complete user profile, using basic info");
      return fallbackProfile;
    } finally {
      setIsLoading(false);
    }
  }, [currentUser, call, getCachedProfile, setCachedProfile]);

  // Refresh user profile
  const refreshProfile = useCallback(() => {
    return fetchUserProfile(true);
  }, [fetchUserProfile]);

  // Check if user has specific role
  const hasRole = useCallback((role: string): boolean => {
    return userProfile?.roles?.includes(role) || false;
  }, [userProfile]);

  // Check if user has specific permission
  const hasPermission = useCallback((doctype: string, permission: string): boolean => {
    return userProfile?.permissions?.[doctype]?.[permission] || false;
  }, [userProfile]);

  // Initialize profile
  useEffect(() => {
    if (!currentUser) {
      clearCache();
      return;
    }

    // Load initial data only once
    fetchUserProfile();
  }, [currentUser, fetchUserProfile, clearCache]);

  // Clear cache when user logs out
  useEffect(() => {
    if (!currentUser && userProfile) {
      clearCache();
    }
  }, [currentUser, userProfile, clearCache]);

  return {
    userProfile,
    isLoading,
    error,
    refreshProfile,
    clearCache,
    hasRole,
    hasPermission,
    getCachedProfile
  };
}
