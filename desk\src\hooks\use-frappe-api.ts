"use client";

import { useSession } from "next-auth/react";
import { useState, useCallback } from "react";

// NextAuth ile authenticated o<PERSON> k<PERSON> için Frappe API calls
export function useFrappeAPI() {
  const { data: session } = useSession();
  const [loading, setLoading] = useState(false);

  const makeCall = useCallback(async (method: string, args?: any) => {
    if (!session?.user) {
      throw new Error("Not authenticated");
    }

    setLoading(true);
    try {
      // Frappe'ye NextAuth session bilgisi ile API call yap
      const response = await fetch(`http://localhost:8000/api/method/${method}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Frappe-User': session.user.id, // NextAuth user'ını Frappe'ye gönder
        },
        body: JSON.stringify(args || {}),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } finally {
      setLoading(false);
    }
  }, [session]);

  const getCall = useCallback(async (method: string, args?: any) => {
    if (!session?.user) {
      throw new Error("Not authenticated");
    }

    setLoading(true);
    try {
      const queryString = args ? `?${new URLSearchParams(args).toString()}` : '';
      const response = await fetch(`http://localhost:8000/api/method/${method}${queryString}`, {
        method: 'GET',
        headers: {
          'X-Frappe-User': session.user.id, // NextAuth user'ını Frappe'ye gönder
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } finally {
      setLoading(false);
    }
  }, [session]);

  // Frappe'ye login yap ve session oluştur
  const createFrappeSession = useCallback(async () => {
    if (!session?.user) {
      throw new Error("Not authenticated with NextAuth");
    }

    try {
      // Frappe'ye login API call yap
      const response = await fetch('http://localhost:8000/api/method/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          usr: session.user.id,
          pwd: session.user.id // Frappe'de aynı username/password olduğunu varsayıyoruz
        }),
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        console.log("Frappe session created:", data);
        return data;
      } else {
        throw new Error(`Login failed: ${response.status}`);
      }
    } catch (error) {
      console.error("Error creating Frappe session:", error);
      throw error;
    }
  }, [session]);

  return {
    call: makeCall,
    get: getCall,
    createFrappeSession,
    loading,
    isAuthenticated: !!session?.user
  };
}
