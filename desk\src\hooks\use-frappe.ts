"use client";

import { useSession } from "next-auth/react";
import {
  useFrap<PERSON>A<PERSON>,
  useFrappeGetDoc,
  useFrappeGetDocList,
  useFrappeCreateDoc,
  useFrappeUpdateDoc,
  useFrappeDeleteDoc,
  useFrappePostCall,
  useFrappeGetCall
} from "frappe-react-sdk";
import { useEffect } from "react";

// Custom hook for Frappe authentication with NextAuth integration
export function useFrappeWithAuth() {
  const { data: session, status } = useSession();
  const { currentUser, isValidating, login, logout, updateCurrentUser } = useFrappeAuth();

  // NextAuth ile login olduktan sonra Frappe'ye de login yap
  useEffect(() => {
    const syncAuth = async () => {
      if (status === "authenticated" && session?.user && !currentUser) {
        try {
          // NextAuth'dan gelen bilgilerle Frappe'ye login yap
          await login(session.user.id, "dummy-password"); // Frappe zaten authenticated
        } catch (error) {
          console.warn("Could not sync Frappe auth:", error);
        }
      }
    };

    syncAuth();
  }, [status, session, currentUser, login]);

  return {
    // NextAuth session data
    session,
    sessionStatus: status,

    // Frappe authentication data
    currentUser,
    isValidating,

    // Authentication methods
    login,
    logout,
    updateCurrentUser,

    // Helper methods
    isAuthenticated: status === "authenticated"
  };
}

// Custom hook for Frappe document operations
export function useFrappeDoc(doctype: string, name?: string) {
  const { data, error, isValidating, mutate } = useFrappeGetDoc(doctype, name);

  return {
    doc: data,
    error,
    isLoading: isValidating,
    refresh: mutate
  };
}

// Custom hook for Frappe document list operations
export function useFrappeDocList(
  doctype: string,
  args?: {
    fields?: string[];
    filters?: Record<string, any>;
    orderBy?: string;
    limit?: number;
    asDict?: boolean;
  }
) {
  const { data, error, isValidating, mutate } = useFrappeGetDocList(
    doctype,
    args
  );

  return {
    docs: data,
    error,
    isLoading: isValidating,
    refresh: mutate
  };
}

// Custom hook for creating documents
export function useFrappeCreate() {
  const { createDoc, loading, error } = useFrappeCreateDoc();

  return {
    createDoc,
    isCreating: loading,
    error
  };
}

// Custom hook for updating documents
export function useFrappeUpdate() {
  const { updateDoc, loading, error } = useFrappeUpdateDoc();

  return {
    updateDoc,
    isUpdating: loading,
    error
  };
}

// Custom hook for deleting documents
export function useFrappeDelete() {
  const { deleteDoc, loading, error } = useFrappeDeleteDoc();

  return {
    deleteDoc,
    isDeleting: loading,
    error
  };
}

// Custom hook for Frappe API calls using React SDK
export function useFrappeCall() {
  // Frappe React SDK hook'larını kullan
  const { call: postCall } = useFrappePostCall();
  const { call: getCall } = useFrappeGetCall();

  return {
    call: postCall,
    get: getCall
  };
}
