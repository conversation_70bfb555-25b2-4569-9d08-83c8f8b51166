"use client";

import { useSession } from "next-auth/react";
import { useFrappeA<PERSON>, useFrappeGetDoc, useFrappeGetDocList, useFrappeCreateDoc, useFrappeUpdateDoc, useFrappeDeleteDoc } from "frappe-react-sdk";
import { useEffect, useState } from "react";
import { frappeUtils } from "@/lib/frappe/frappeClient";

// Custom hook for Frappe authentication with NextAuth integration
export function useFrappeWithAuth() {
  const { data: session, status } = useSession();
  const { currentUser, isValidating, login, logout, updateCurrentUser } = useFrappeAuth();

  // Sync NextAuth session with Frappe authentication
  useEffect(() => {
    if (status === "authenticated" && session?.sid && !currentUser) {
      // If NextAuth is authenticated but Frappe is not, sync the session
      updateCurrentUser();
    } else if (status === "unauthenticated" && currentUser) {
      // If NextAuth is not authenticated but <PERSON>ap<PERSON> is, logout from Frappe
      logout();
    }
  }, [status, session, currentUser, updateCurrentUser, logout]);

  return {
    // NextAuth session data
    session,
    sessionStatus: status,

    // Frappe authentication data
    currentUser,
    isValidating,

    // Authentication methods
    login,
    logout,
    updateCurrentUser,

    // Helper methods
    isAuthenticated: status === "authenticated" && !!currentUser,
    frappeUtils
  };
}

// Custom hook for Frappe document operations
export function useFrappeDoc(doctype: string, name?: string) {
  const { data, error, isValidating, mutate } = useFrappeGetDoc(doctype, name);

  return {
    doc: data,
    error,
    isLoading: isValidating,
    refresh: mutate
  };
}

// Custom hook for Frappe document list operations
export function useFrappeDocList(
  doctype: string,
  args?: {
    fields?: string[];
    filters?: Record<string, any>;
    orderBy?: string;
    limit?: number;
    asDict?: boolean;
  }
) {
  const { data, error, isValidating, mutate } = useFrappeGetDocList(
    doctype,
    args
  );

  return {
    docs: data,
    error,
    isLoading: isValidating,
    refresh: mutate
  };
}

// Custom hook for creating documents
export function useFrappeCreate() {
  const { createDoc, loading, error } = useFrappeCreateDoc();

  return {
    createDoc,
    isCreating: loading,
    error
  };
}

// Custom hook for updating documents
export function useFrappeUpdate() {
  const { updateDoc, loading, error } = useFrappeUpdateDoc();

  return {
    updateDoc,
    isUpdating: loading,
    error
  };
}

// Custom hook for deleting documents
export function useFrappeDelete() {
  const { deleteDoc, loading, error } = useFrappeDeleteDoc();

  return {
    deleteDoc,
    isDeleting: loading,
    error
  };
}

// Custom hook for Frappe API calls
export function useFrappeCall() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const makeCall = async (method: string, args?: any) => {
    if (!isClient) return null;

    try {
      // Import frappe dynamically to avoid SSR issues
      const { frappe } = await import("@/lib/frappe/frappeClient");
      const response = await frappe.call().post(method, args);
      return response;
    } catch (error) {
      console.error(`Error calling ${method}:`, error);
      throw error;
    }
  };

  const makeGetCall = async (method: string, args?: any) => {
    if (!isClient) return null;

    try {
      // Import frappe dynamically to avoid SSR issues
      const { frappe } = await import("@/lib/frappe/frappeClient");
      const response = await frappe.call().get(method, args);
      return response;
    } catch (error) {
      console.error(`Error calling ${method}:`, error);
      throw error;
    }
  };

  return {
    call: makeCall,
    get: makeGetCall
  };
}
