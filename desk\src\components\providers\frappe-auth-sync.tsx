"use client";

import { useEffect } from "react";
import { useSession } from "next-auth/react";
import { useFrappeAuth } from "frappe-react-sdk";

interface FrappeAuthSyncProps {
  children: React.ReactNode;
}

export function FrappeAuthSync({ children }: FrappeAuthSyncProps) {
  const { data: session, status } = useSession();
  const { currentUser, login, logout } = useFrappeAuth();

  useEffect(() => {
    const syncAuth = async () => {
      console.log("Auth sync check:", {
        nextAuthStatus: status,
        nextAuthUser: session?.user?.id,
        frappeUser: currentUser
      });

      if (status === "authenticated" && session?.user) {
        // NextAuth authenticated but Frappe not
        if (!currentUser) {
          try {
            console.log("Attempting Frappe login for:", session.user.id);
            // Frappe'ye login yap - NextAuth'da zaten doğ<PERSON>ı, sadece session'ı sync et
            await login({
              username: session.user.id,
              password: "authenticated-via-nextauth"
            });
          } catch (error) {
            console.error("Frappe login failed:", error);
          }
        }
      } else if (status === "unauthenticated") {
        // NextAuth not authenticated but Frappe is
        if (currentUser) {
          try {
            console.log("Logging out from Frappe");
            await logout();
          } catch (error) {
            console.error("Frappe logout failed:", error);
          }
        }
      }
    };

    // Sadece status değiştiğinde sync yap
    if (status !== "loading") {
      syncAuth();
    }
  }, [status, session?.user?.id, currentUser, login, logout]);

  return <>{children}</>;
}
