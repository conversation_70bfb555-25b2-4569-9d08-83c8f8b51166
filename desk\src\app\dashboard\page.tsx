"use client";

import { AuthGuard } from "@/components/auth/auth-guard";
import { AppLayout } from '@/components/layout/app-layout'

function DashboardContent() {
  return (
    <AppLayout>
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Dashboard</h1>
          <p className="text-gray-600">Welcome to IoT Gateway</p>
        </div>
      </div>
    </AppLayout>
  );
}

export default function Dashboard() {
  return (
    <AuthGuard>
      <DashboardContent />
    </AuthGuard>
  );
}
