"use client";

import { useSession } from "next-auth/react";
import { useFrappeAuth } from "frappe-react-sdk";
import { useFrappeCall } from "@/hooks/use-frappe";
import { useState } from "react";
import EnhancedProductTable from '@/components/examples/enhanced-product-table'
import { AppLayout } from '@/components/layout/app-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function Dashboard() {
  const { data: session } = useSession();
  const { currentUser } = useFrappeAuth();
  const { call, getDocList } = useFrappeCall();
  const [testResult, setTestResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const testFrappeConnection = async () => {
    setIsLoading(true);
    try {
      // Test Frappe connection with cookie
      const result = await call('frappe.auth.get_logged_user');
      setTestResult(result);
    } catch (error) {
      setTestResult({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
        </div>

        {/* User Info Card */}
        <Card>
          <CardHeader>
            <CardTitle>Welcome Back!</CardTitle>
            <CardDescription>Your session information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">NextAuth User</p>
                <p className="text-lg">{session?.user?.full_name || session?.user?.id}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Frappe User</p>
                <p className="text-lg">{currentUser || "Not connected"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Authentication Status</p>
                <p className="text-lg">{session ? "✅ Authenticated" : "❌ Not Authenticated"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Session ID</p>
                <p className="text-lg font-mono text-xs">{session?.sid || "No SID"}</p>
              </div>
            </div>
            <div className="mt-4">
              <Button onClick={testFrappeConnection} disabled={isLoading}>
                {isLoading ? "Testing..." : "Test Frappe Connection"}
              </Button>
              {testResult && (
                <div className="mt-2 p-2 bg-gray-100 rounded">
                  <pre className="text-xs">{JSON.stringify(testResult, null, 2)}</pre>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 gap-6">
          <EnhancedProductTable />
        </div>
      </div>
    </AppLayout>
  )
}
