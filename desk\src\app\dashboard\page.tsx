"use client";

import { use<PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON>rap<PERSON><PERSON>et<PERSON><PERSON>, useFrappeGetDocList } from "frappe-react-sdk";
import { useState } from "react";
import { useUserCache } from "@/hooks/use-user-cache";
import { AuthGuard } from "@/components/auth/auth-guard";
import EnhancedProductTable from '@/components/examples/enhanced-product-table'
import { AppLayout } from '@/components/layout/app-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

function DashboardContent() {
  const { currentUser } = useFrappeAuth();
  const { data: authData, error: authError } = useFrappeGetCall('frappe.auth.get_logged_user');
  const { userProfile, isLoading: profileLoading } = useUserCache();
  const [testResult, setTestResult] = useState<any>(null);

  // Test Frappe API connection - sadece enabled users
  const { data: users, isLoading: usersLoading } = useFrappeGetDocList("User", {
    fields: ["name", "full_name", "email"],
    limit: 5
  });

  const testFrappeConnection = () => {
    setTestResult({
      auth_api_result: authData,
      auth_api_error: authError,
      current_frappe_user: currentUser,
      user_profile: userProfile,
      cookies: document.cookie,
      users_data: users?.slice(0, 3) // İlk 3 user'ı göster
    });
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
        </div>

        {/* User Info Card */}
        <Card>
          <CardHeader>
            <CardTitle>Welcome Back!</CardTitle>
            <CardDescription>Your Frappe session information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Current User</p>
                <p className="text-lg">{currentUser || "Not logged in"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Authentication Status</p>
                <p className="text-lg">{currentUser ? "✅ Authenticated" : "❌ Not Authenticated"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Profile Loading</p>
                <p className="text-lg">{profileLoading ? "🔄 Loading..." : "✅ Ready"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Users Data</p>
                <p className="text-lg">{usersLoading ? "🔄 Loading..." : users ? `${users.length} users` : "No data"}</p>
              </div>
            </div>
            <div className="mt-4">
              <Button onClick={testFrappeConnection}>
                Test Frappe API
              </Button>
              {testResult && (
                <div className="mt-2 p-2 bg-gray-100 rounded">
                  <pre className="text-xs overflow-auto max-h-40">{JSON.stringify(testResult, null, 2)}</pre>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Users List Card */}
        {users && users.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Frappe Users</CardTitle>
              <CardDescription>Recent users from your Frappe instance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {users.map((user: any) => (
                  <div key={user.name} className="flex justify-between items-center p-2 border rounded">
                    <div>
                      <p className="font-medium">{user.full_name || user.name}</p>
                      <p className="text-sm text-gray-500">{user.email}</p>
                    </div>
                    <span className={`px-2 py-1 rounded text-xs ${
                      user.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {user.enabled ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 gap-6">
          <EnhancedProductTable />
        </div>
      </div>
    </AppLayout>
  );
}

export default function Dashboard() {
  return (
    <AuthGuard>
      <DashboardContent />
    </AuthGuard>
  );
}
