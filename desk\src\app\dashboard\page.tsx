"use client";

import { useFrappeGetDocList } from "frappe-react-sdk";
import { useUserInfo } from "@/hooks/use-user-info";
import { AuthGuard } from "@/components/auth/auth-guard";
import EnhancedProductTable from '@/components/examples/enhanced-product-table'
import { AppLayout } from '@/components/layout/app-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

function DashboardContent() {
  const userInfo = useUserInfo();

  // Sample data for dashboard
  const { data: users } = useFrappeGetDocList("User", {
    fields: ["name", "full_name", "email"],
    limit: 5
  });

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
        </div>

        {/* Welcome Card */}
        <Card>
          <CardHeader>
            <CardTitle>Welcome Back, {userInfo.full_name}!</CardTitle>
            <CardDescription>IoT Gateway Management Dashboard</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">10K+</p>
                <p className="text-sm text-gray-500">Active Devices</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">99.9%</p>
                <p className="text-sm text-gray-500">Uptime</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600">24/7</p>
                <p className="text-sm text-gray-500">Support</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Users List Card */}
        {users && users.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Frappe Users</CardTitle>
              <CardDescription>Recent users from your Frappe instance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {users.map((user: any) => (
                  <div key={user.name} className="flex justify-between items-center p-2 border rounded">
                    <div>
                      <p className="font-medium">{user.full_name || user.name}</p>
                      <p className="text-sm text-gray-500">{user.email}</p>
                    </div>
                    <span className={`px-2 py-1 rounded text-xs ${
                      user.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {user.enabled ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 gap-6">
          <EnhancedProductTable />
        </div>
      </div>
    </AppLayout>
  );
}

export default function Dashboard() {
  return (
    <AuthGuard>
      <DashboardContent />
    </AuthGuard>
  );
}
