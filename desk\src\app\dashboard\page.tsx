"use client";

import { useSession } from "next-auth/react";
import { useFrappeAuth } from "frappe-react-sdk";
import { useFrappeAPI } from "@/hooks/use-frappe-api";
import { useState, useEffect } from "react";
import EnhancedProductTable from '@/components/examples/enhanced-product-table'
import { AppLayout } from '@/components/layout/app-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function Dashboard() {
  const { data: session } = useSession();
  const { currentUser } = useFrappeAuth();
  const { call, createFrappeSession, isAuthenticated } = useFrappeAPI();
  const [testResult, setTestResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [sessionCreated, setSessionCreated] = useState(false);

  // NextAuth ile login olduktan sonra Frappe session oluştur
  useEffect(() => {
    const initFrappeSession = async () => {
      if (isAuthenticated && !sessionCreated) {
        try {
          console.log("Creating Frappe session...");
          await createFrappeSession();
          setSessionCreated(true);
          console.log("Frappe session created successfully");
        } catch (error) {
          console.error("Failed to create Frappe session:", error);
        }
      }
    };

    initFrappeSession();
  }, [isAuthenticated, sessionCreated, createFrappeSession]);

  const testFrappeConnection = async () => {
    setIsLoading(true);
    try {
      // Test Frappe connection - whoami API'si mevcut user bilgilerini döndürür
      const result = await call('frappe.auth.get_logged_user');
      setTestResult({
        api_result: result,
        current_frappe_user: currentUser,
        nextauth_user: session?.user?.id,
        cookies: document.cookie
      });
    } catch (error) {
      setTestResult({
        error: error?.message || 'Unknown error',
        current_frappe_user: currentUser,
        nextauth_user: session?.user?.id,
        cookies: document.cookie
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
        </div>

        {/* User Info Card */}
        <Card>
          <CardHeader>
            <CardTitle>Welcome Back!</CardTitle>
            <CardDescription>Your session information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">NextAuth User</p>
                <p className="text-lg">{session?.user?.full_name || session?.user?.id}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Frappe User</p>
                <p className="text-lg">{currentUser || "Not connected"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Authentication Status</p>
                <p className="text-lg">{session ? "✅ Authenticated" : "❌ Not Authenticated"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Frappe Session</p>
                <p className="text-lg">{sessionCreated ? "✅ Created" : "❌ Not Created"}</p>
              </div>
            </div>
            <div className="mt-4">
              <Button onClick={testFrappeConnection} disabled={isLoading}>
                {isLoading ? "Testing..." : "Test Frappe API"}
              </Button>
              {testResult && (
                <div className="mt-2 p-2 bg-gray-100 rounded">
                  <pre className="text-xs">{JSON.stringify(testResult, null, 2)}</pre>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 gap-6">
          <EnhancedProductTable />
        </div>
      </div>
    </AppLayout>
  )
}
