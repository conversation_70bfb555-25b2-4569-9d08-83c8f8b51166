"use client";

import { useSession } from "next-auth/react";
import { useFrappeWithAuth, useFrappeDocList } from "@/hooks/use-frappe";
import EnhancedProductTable from '@/components/examples/enhanced-product-table'
import { AppLayout } from '@/components/layout/app-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function Dashboard() {
  const { data: session } = useSession();
  const { currentUser, isAuthenticated } = useFrappeWithAuth();

  // Example: Fetch some Frappe documents (you can change this to your actual doctype)
  const { docs: users, isLoading: usersLoading } = useFrappeDocList("User", {
    fields: ["name", "full_name", "email", "enabled"],
    limit: 5
  });

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
        </div>

        {/* User Info Card */}
        <Card>
          <CardHeader>
            <CardTitle>Welcome Back!</CardTitle>
            <CardDescription>Your session information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">NextAuth User</p>
                <p className="text-lg">{session?.user?.full_name || session?.user?.id}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Frappe User</p>
                <p className="text-lg">{currentUser || "Not connected"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Authentication Status</p>
                <p className="text-lg">{isAuthenticated ? "✅ Authenticated" : "❌ Not Authenticated"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Session ID</p>
                <p className="text-lg font-mono text-xs">{session?.sid || "No SID"}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Frappe Data Example */}
        <Card>
          <CardHeader>
            <CardTitle>Frappe Users</CardTitle>
            <CardDescription>Recent users from your Frappe instance</CardDescription>
          </CardHeader>
          <CardContent>
            {usersLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            ) : users && users.length > 0 ? (
              <div className="space-y-2">
                {users.map((user: any) => (
                  <div key={user.name} className="flex justify-between items-center p-2 border rounded">
                    <div>
                      <p className="font-medium">{user.full_name || user.name}</p>
                      <p className="text-sm text-gray-500">{user.email}</p>
                    </div>
                    <span className={`px-2 py-1 rounded text-xs ${user.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                      {user.enabled ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No users found or not connected to Frappe</p>
            )}
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 gap-6">
          <EnhancedProductTable />
        </div>
      </div>
    </AppLayout>
  )
}
