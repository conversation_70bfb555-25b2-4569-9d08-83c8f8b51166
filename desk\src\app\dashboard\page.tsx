"use client";

import { useFrap<PERSON>A<PERSON>, useFrappePostCall, useFrappeGetDocList } from "frappe-react-sdk";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import EnhancedProductTable from '@/components/examples/enhanced-product-table'
import { AppLayout } from '@/components/layout/app-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

export default function Dashboard() {
  const router = useRouter();
  const { currentUser, isValidating } = useFrappeAuth();
  const { call } = useFrappePostCall();
  const [testResult, setTestResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Authentication guard - daha toleranslı yaklaşım
  useEffect(() => {
    console.log("Auth state:", { currentUser, isValidating });

    // Sadece loading tamamen bittikten sonra ve kesinlikle user yoksa yönlendir
    const timer = setTimeout(() => {
      if (!isValidating && !currentUser) {
        console.log("Redirecting to login - no user found");
        router.push('/login');
      }
    }, 1000); // 1 saniye bekle

    return () => clearTimeout(timer);
  }, [currentUser, isValidating, router]);

  // Test Frappe API connection
  const { data: users, error: usersError, isLoading: usersLoading } = useFrappeGetDocList("User", {
    fields: ["name", "full_name", "email", "enabled"],
    filters: { enabled: 1 },
    limit: 5
  });

  const testFrappeConnection = async () => {
    setIsLoading(true);
    try {
      // Test Frappe connection - whoami API'si mevcut user bilgilerini döndürür
      const result = await call('frappe.auth.get_logged_user');
      setTestResult({
        api_result: result,
        current_frappe_user: currentUser,
        cookies: document.cookie,
        users_data: users?.slice(0, 3) // İlk 3 user'ı göster
      });
    } catch (error) {
      setTestResult({
        error: error?.message || 'Unknown error',
        current_frappe_user: currentUser,
        cookies: document.cookie
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Loading state - authentication kontrol ediliyor
  if (isValidating) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-gray-600">Checking authentication...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  // Not authenticated - redirect will happen via useEffect
  if (!currentUser) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-gray-600">Redirecting to login...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
        </div>

        {/* User Info Card */}
        <Card>
          <CardHeader>
            <CardTitle>Welcome Back!</CardTitle>
            <CardDescription>Your Frappe session information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Current User</p>
                <p className="text-lg">{currentUser || "Not logged in"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Authentication Status</p>
                <p className="text-lg">{currentUser ? "✅ Authenticated" : "❌ Not Authenticated"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Session Loading</p>
                <p className="text-lg">{isValidating ? "🔄 Loading..." : "✅ Ready"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Users Data</p>
                <p className="text-lg">{usersLoading ? "🔄 Loading..." : users ? `${users.length} users` : "No data"}</p>
              </div>
            </div>
            <div className="mt-4">
              <Button onClick={testFrappeConnection} disabled={isLoading}>
                {isLoading ? "Testing..." : "Test Frappe API"}
              </Button>
              {testResult && (
                <div className="mt-2 p-2 bg-gray-100 rounded">
                  <pre className="text-xs overflow-auto max-h-40">{JSON.stringify(testResult, null, 2)}</pre>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Users List Card */}
        {users && users.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Frappe Users</CardTitle>
              <CardDescription>Recent users from your Frappe instance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {users.map((user: any) => (
                  <div key={user.name} className="flex justify-between items-center p-2 border rounded">
                    <div>
                      <p className="font-medium">{user.full_name || user.name}</p>
                      <p className="text-sm text-gray-500">{user.email}</p>
                    </div>
                    <span className={`px-2 py-1 rounded text-xs ${
                      user.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {user.enabled ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 gap-6">
          <EnhancedProductTable />
        </div>
      </div>
    </AppLayout>
  )
}
