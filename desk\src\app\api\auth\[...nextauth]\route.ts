import NextAuth from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";

// <PERSON><PERSON> kullan<PERSON> tipi tanımlama
type CustomUser = {
  id: string;
  full_name?: string;
  email?: string;
}

// Session tipi genişletme
declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      full_name?: string;
      email?: string;
      name?: string | null;
      image?: string | null;
    }
  }

  interface JWT {
    id?: string;
    full_name?: string;
    email?: string;
  }
}

// Frappe URL configuration
const FRAPPE_URL = process.env.FRAPPE_URL || 'http://localhost:8000';

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Frappe Credentials",
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          return null;
        }

        try {
          // Frappe'ye do<PERSON><PERSON>an <PERSON> request gönder
          const loginResponse = await fetch(`${FRAPPE_URL}/api/method/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              usr: credentials.username,
              pwd: credentials.password
            })
          });

          if (!loginResponse.ok) {
            console.error("Login failed with status:", loginResponse.status);
            return null;
          }

          const loginData = await loginResponse.json();
          console.info("Login successful:", loginData);

          return {
            id: credentials.username,
            full_name: loginData.full_name || credentials.username,
            email: credentials.username
          } as CustomUser;
        } catch (error) {
          console.error("Login failed:", error);
          return null;
        }
      }
    })
  ],
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  callbacks: {
    async session({ session, token }) {
      if (token && session?.user) {
        session.user.id = token.id as string;
        session.user.full_name = token.full_name as string;
        session.user.email = token.email as string;
      }
      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.full_name = (user as CustomUser).full_name;
        token.email = (user as CustomUser).email;
      }
      return token;
    },
    async signIn({ user }) {
      // Login başarılı olduktan sonra Frappe session'ını da oluştur
      if (user) {
        try {
          // Frappe'ye session oluşturma isteği gönder
          const sessionResponse = await fetch(`${FRAPPE_URL}/api/method/frappe.auth.login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              usr: user.id,
              pwd: "authenticated-via-nextauth" // Dummy password, zaten doğrulandı
            })
          });

          if (sessionResponse.ok) {
            console.log("Frappe session created successfully");
          } else {
            console.warn("Could not create Frappe session");
          }
        } catch (error) {
          console.error("Error creating Frappe session:", error);
        }
      }
      return true;
    }
  },
  // CSRF korumasını etkinleştir
  secret: process.env.NEXTAUTH_SECRET || "geçici-gizli-anahtar",
  useSecureCookies: process.env.NODE_ENV === "production",
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === "production"
        ? `__Secure-next-auth.session-token`
        : `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production"
      }
    },
    csrfToken: {
      name: process.env.NODE_ENV === "production"
        ? `__Host-next-auth.csrf-token`
        : `next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production"
      }
    }
  }
});

export { handler as GET, handler as POST };