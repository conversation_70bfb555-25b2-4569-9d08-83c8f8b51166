import NextAuth from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";

// <PERSON><PERSON> k<PERSON> tipi tanımlama
type CustomUser = {
  id: string;
  full_name?: string;
  home_page?: string;
  message?: string;
  email?: string;
  sid?: string;
}

// Session tipi genişletme
declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      full_name?: string;
      home_page?: string;
      message?: string;
      email?: string;
      name?: string | null;
      image?: string | null;
    }
    sid?: string;
    frappeCookies?: any;
  }

  interface JWT {
    id?: string;
    full_name?: string;
    home_page?: string;
    message?: string;
    email?: string;
    sid?: string;
    frappeCookies?: any;
  }
}

// Frappe URL configuration
const FRAPPE_URL = process.env.FRAPPE_URL || 'http://localhost:8000';

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Frappe Credentials",
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          return null;
        }

        try {
          // Frappe'ye doğrudan HTTP request gönder
          const loginResponse = await fetch(`${FRAPPE_URL}/api/method/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              usr: credentials.username,
              pwd: credentials.password
            }),
            credentials: 'include' // Cookie'leri dahil et
          });

          if (!loginResponse.ok) {
            console.error("Login failed with status:", loginResponse.status);
            return null;
          }

          const loginData = await loginResponse.json();
          console.info("Login data:", loginData);

          // Cookie'leri al
          const cookies = loginResponse.headers.get('set-cookie');
          console.info("Frappe cookies:", cookies);

          // User bilgilerini al
          const userResponse = await fetch('http://localhost:8000/api/method/frappe.auth.get_logged_user', {
            method: 'GET',
            headers: {
              'Cookie': cookies || '',
            },
            credentials: 'include'
          });

          let userInfo = null;
          if (userResponse.ok) {
            userInfo = await userResponse.json();
          }

          return {
            id: credentials.username,
            message: loginData.message,
            home_page: loginData.home_page,
            full_name: loginData.full_name,
            email: userInfo?.message || credentials.username,
            sid: "frappe-session",
            frappeCookies: cookies // Cookie'leri sakla
          } as CustomUser & { frappeCookies: string };
        } catch (error) {
          console.error("Login failed:", error);
          return null;
        }
      }
    })
  ],
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  callbacks: {
    async session({ session, token }) {
      if (token && session?.user) {
        session.user.id = token.id as string;
        session.user.full_name = token.full_name as string;
        session.user.home_page = token.home_page as string;
        session.user.message = token.message as string;
        session.user.email = token.email as string;
        session.sid = token.sid as string;

        // Frappe cookie'lerini session'a ekle
        if (token.frappeCookies) {
          session.frappeCookies = token.frappeCookies as any;
        }
      }
      return session;
    },
    async jwt({ token, user, account }) {
      if (user && account) {
        token.id = user.id;
        token.full_name = (user as CustomUser).full_name;
        token.home_page = (user as CustomUser).home_page;
        token.message = (user as CustomUser).message;
        token.email = (user as CustomUser).email;
        token.sid = (user as CustomUser).sid;

        // Frappe login sonrası cookie'leri sakla
        if ((user as any).frappeCookies) {
          token.frappeCookies = (user as any).frappeCookies;
        }
      }
      return token;
    },
    async signIn({ user, account, profile }) {
      // Login başarılı olduğunda Frappe cookie'lerini tarayıcıya set et
      if (user && (user as any).frappeCookies) {
        // Bu callback'te response header'larına erişemiyoruz
        // Bu yüzden cookie'leri başka bir yolla set etmemiz gerekiyor
        return true;
      }
      return true;
    }
  },
  // CSRF korumasını etkinleştir
  secret: process.env.NEXTAUTH_SECRET || "geçici-gizli-anahtar",
  useSecureCookies: process.env.NODE_ENV === "production",
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === "production"
        ? `__Secure-next-auth.session-token`
        : `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production"
      }
    },
    csrfToken: {
      name: process.env.NODE_ENV === "production"
        ? `__Host-next-auth.csrf-token`
        : `next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production"
      }
    }
  }
});

export { handler as GET, handler as POST };