import NextAuth from "next-auth";
import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials"
import { FrappeApp } from "frappe-js-sdk";

// <PERSON><PERSON> kullanıcı tipi tanımlama
type CustomUser = {
  id: string;
  full_name?: string;
  home_page?: string;
  message?: string;
  email?: string;
  sid?: string;
}

// Session tipi genişletme
declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      full_name?: string;
      home_page?: string;
      message?: string;
      email?: string;
      name?: string | null;
      image?: string | null;
    }
    sid?: string;
  }

  interface JWT {
    id?: string;
    full_name?: string;
    home_page?: string;
    message?: string;
    email?: string;
    sid?: string;
  }
}

// Frappe bağlantısını SSR güvenli şekilde oluştur
const frappe = new FrappeApp('http://localhost:8000');

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Frappe Credentials",
      credentials: {
        username: { label: "<PERSON>rname", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.username || !credentials?.password) {
          return null;
        }

        try {
          const auth = frappe.auth();
          const response = await auth.loginWithUsernamePassword({
            username: credentials.username,
            password: credentials.password
          });

          console.info("Login data:", response);

          // Frappe session bilgilerini al
          let sessionInfo = null;
          try {
            sessionInfo = await frappe.call().get('frappe.auth.get_logged_user');
          } catch (error) {
            console.warn('Could not get session info:', error);
          }

          return {
            id: credentials.username,
            message: response.message,
            home_page: response.home_page,
            full_name: response.full_name,
            email: sessionInfo?.message || credentials.username,
            sid: "session-id-placeholder" // Will be managed by React SDK
          } as CustomUser;
        } catch (error) {
          console.error("Login failed:", error);
          return null;
        }
      }
    })
  ],
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  callbacks: {
    async session({ session, token }) {
      if (token && session?.user) {
        session.user.id = token.id as string;
        session.user.full_name = token.full_name as string;
        session.user.home_page = token.home_page as string;
        session.user.message = token.message as string;
        session.user.email = token.email as string;
        session.sid = token.sid as string;
      }
      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.full_name = (user as CustomUser).full_name;
        token.home_page = (user as CustomUser).home_page;
        token.message = (user as CustomUser).message;
        token.email = (user as CustomUser).email;
        token.sid = (user as CustomUser).sid;
      }
      return token;
    }
  },
  // CSRF korumasını etkinleştir
  secret: process.env.NEXTAUTH_SECRET || "geçici-gizli-anahtar",
  useSecureCookies: process.env.NODE_ENV === "production",
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
  },
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === "production"
        ? `__Secure-next-auth.session-token`
        : `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production"
      }
    },
    csrfToken: {
      name: process.env.NODE_ENV === "production"
        ? `__Host-next-auth.csrf-token`
        : `next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production"
      }
    }
  }
});

export { handler as GET, handler as POST };