"use client";

import { ReactNode } from "react";
import { useSessionManager } from "@/hooks/use-session-manager";
import { useUserCache } from "@/hooks/use-user-cache";

interface SessionManagerProviderProps {
  children: ReactNode;
}

export function SessionManagerProvider({ children }: SessionManagerProviderProps) {
  // Initialize session management
  useSessionManager({
    inactivityTimeout: 5 * 60 * 1000, // 5 minutes
    sessionCheckInterval: 30 * 1000, // 30 seconds
    warningBeforeLogout: 60 * 1000 // 1 minute warning
  });

  // Initialize user cache
  useUserCache({
    ttl: 5 * 60 * 1000, // 5 minutes cache
    refreshInterval: 2 * 60 * 1000 // Refresh every 2 minutes
  });

  return <>{children}</>;
}
