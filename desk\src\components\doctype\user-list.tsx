"use client";

import { useState } from "react";
import { useFrappeDocList, useFrappe<PERSON>reate, useFrappeUpdate, useFrappeDelete } from "@/hooks/use-frappe";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { Plus, Edit, Trash2, Save, X } from "lucide-react";

interface User {
  name: string;
  full_name: string;
  email: string;
  enabled: number;
  creation: string;
}

export function UserList() {
  const [editingUser, setEditingUser] = useState<string | null>(null);
  const [newUser, setNewUser] = useState({ full_name: "", email: "", enabled: 1 });
  const [showNewUserForm, setShowNewUserForm] = useState(false);

  // Fetch users from Frappe
  const { docs: users, isLoading, refresh } = useFrappeDocList("User", {
    fields: ["name", "full_name", "email", "enabled", "creation"],
    filters: { enabled: 1 },
    orderBy: "creation desc",
    limit: 20
  });

  const { createDoc, isCreating } = useFrappeCreate();
  const { updateDoc, isUpdating } = useFrappeUpdate();
  const { deleteDoc, isDeleting } = useFrappeDelete();

  const handleCreateUser = async () => {
    try {
      await createDoc("User", newUser);
      toast.success("User created successfully");
      setNewUser({ full_name: "", email: "", enabled: 1 });
      setShowNewUserForm(false);
      refresh();
    } catch (error) {
      toast.error("Failed to create user");
      console.error(error);
    }
  };

  const handleUpdateUser = async (name: string, data: Partial<User>) => {
    try {
      await updateDoc("User", name, data);
      toast.success("User updated successfully");
      setEditingUser(null);
      refresh();
    } catch (error) {
      toast.error("Failed to update user");
      console.error(error);
    }
  };

  const handleDeleteUser = async (name: string) => {
    if (confirm("Are you sure you want to delete this user?")) {
      try {
        await deleteDoc("User", name);
        toast.success("User deleted successfully");
        refresh();
      } catch (error) {
        toast.error("Failed to delete user");
        console.error(error);
      }
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Users</CardTitle>
          <CardDescription>Loading users...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Users</CardTitle>
            <CardDescription>Manage system users</CardDescription>
          </div>
          <Button onClick={() => setShowNewUserForm(true)} disabled={showNewUserForm}>
            <Plus className="mr-2 h-4 w-4" />
            Add User
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {/* New User Form */}
        {showNewUserForm && (
          <div className="mb-6 p-4 border rounded-lg bg-gray-50">
            <h3 className="text-lg font-medium mb-4">Create New User</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="full_name">Full Name</Label>
                <Input
                  id="full_name"
                  value={newUser.full_name}
                  onChange={(e) => setNewUser({ ...newUser, full_name: e.target.value })}
                  placeholder="Enter full name"
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={newUser.email}
                  onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                  placeholder="Enter email"
                />
              </div>
            </div>
            <div className="flex gap-2 mt-4">
              <Button onClick={handleCreateUser} disabled={isCreating}>
                <Save className="mr-2 h-4 w-4" />
                {isCreating ? "Creating..." : "Create User"}
              </Button>
              <Button variant="outline" onClick={() => setShowNewUserForm(false)}>
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>
            </div>
          </div>
        )}

        {/* Users List */}
        <div className="space-y-2">
          {users && users.length > 0 ? (
            users.map((user: User) => (
              <div key={user.name} className="flex justify-between items-center p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-4">
                    <div>
                      <p className="font-medium">{user.full_name || user.name}</p>
                      <p className="text-sm text-gray-500">{user.email}</p>
                    </div>
                    <span className={`px-2 py-1 rounded text-xs ${
                      user.enabled ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {user.enabled ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEditingUser(user.name)}
                    disabled={editingUser === user.name}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteUser(user.name)}
                    disabled={isDeleting}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))
          ) : (
            <p className="text-center text-gray-500 py-8">No users found</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
