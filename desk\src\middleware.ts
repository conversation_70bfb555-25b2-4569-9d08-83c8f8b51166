import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Public routes - authentication gerektirmeyen sayfalar
  const publicRoutes = ['/login', '/signup', '/forgot-password', '/reset-password']

  // Static assets ve API routes - bunları hızlıca geç
  const isStaticAsset = pathname.startsWith('/_next') ||
                       pathname.startsWith('/api') ||
                       pathname.includes('.') ||
                       pathname.startsWith('/images') ||
                       pathname.startsWith('/icons') ||
                       pathname.startsWith('/favicon') ||
                       pathname === '/manifest.json' ||
                       pathname === '/robots.txt'

  // Public route veya static asset ise hızlıca geç
  if (publicRoutes.includes(pathname) || isStaticAsset) {
    return NextResponse.next()
  }

  // Root path'i dashboard'a yönlendir
  if (pathname === '/') {
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }

  // Frappe session cookie'lerini kontrol et
  const frappeSessionCookie = request.cookies.get('sid')
  const userIdCookie = request.cookies.get('user_id')
  const fullNameCookie = request.cookies.get('full_name')
  const systemUserCookie = request.cookies.get('system_user')

  // Sessiz middleware - sadece kritik durumlarda log
  // CSRF kontrolü Frappe SDK tarafından otomatik yapılıyor

  // Session yoksa veya Guest ise login'e yönlendir
  if (!frappeSessionCookie ||
      !userIdCookie ||
      userIdCookie.value === 'Guest' ||
      userIdCookie.value === '' ||
      frappeSessionCookie.value === 'Guest' ||
      frappeSessionCookie.value === '') {

    // Sessiz redirect - log kaldırıldı
    const loginUrl = new URL('/login', request.url)
    loginUrl.searchParams.set('callbackUrl', pathname)

    // Response oluştur ve cookie'leri temizle
    const response = NextResponse.redirect(loginUrl)

    // Expired cookie'leri temizle
    response.cookies.set('sid', '', {
      expires: new Date(0),
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production'
    })
    response.cookies.set('user_id', '', {
      expires: new Date(0),
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production'
    })
    response.cookies.set('full_name', '', {
      expires: new Date(0),
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production'
    })
    response.cookies.set('system_user', '', {
      expires: new Date(0),
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production'
    })

    return response
  }

  // Valid session var, devam et
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images (public images)
     * - icons (public icons)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images|icons).*)',
  ],
}
