import { withAuth } from "next-auth/middleware"

export default withAuth(
  function middleware(req) {
    // Add any additional middleware logic here
  },
  {
    callbacks: {
      authorized: ({ token }) => !!token
    },
  }
)

export const config = {
  matcher: [
    // Protect all routes except login, public assets, and API routes
    "/((?!api|login|_next/static|_next/image|favicon.ico|public).*)",
  ]
}
