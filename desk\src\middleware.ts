import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Public routes - authentication gerektirmeyen sayfalar
  const publicRoutes = ['/login', '/signup', '/forgot-password']

  // Static assets ve API routes
  const isStaticAsset = pathname.startsWith('/_next') ||
                       pathname.startsWith('/api') ||
                       pathname.includes('.') ||
                       pathname.startsWith('/images') ||
                       pathname.startsWith('/icons')

  // Public route veya static asset ise geç
  if (publicRoutes.includes(pathname) || isStaticAsset) {
    return NextResponse.next()
  }

  // Frappe session cookie'sini kontrol et
  const frappeSessionCookie = request.cookies.get('sid')
  const userIdCookie = request.cookies.get('user_id')

  // Session yoksa veya Guest ise login'e yönlendir
  if (!frappeSessionCookie ||
      !userIdCookie ||
      userIdCookie.value === 'Guest' ||
      frappeSessionCookie.value === 'Guest') {

    console.log('Middleware: No valid session, redirecting to login')
    const loginUrl = new URL('/login', request.url)
    loginUrl.searchParams.set('callbackUrl', pathname)
    return NextResponse.redirect(loginUrl)
  }

  // Valid session var, devam et
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images (public images)
     * - icons (public icons)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images|icons).*)',
  ],
}
