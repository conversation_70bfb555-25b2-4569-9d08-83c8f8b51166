import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Frappe authentication artık client-side yapılıyor
  // Middleware sadece public routes'ları kontrol ediyor

  const { pathname } = request.nextUrl

  // Login sayfasına erişime izin ver
  if (pathname === '/login') {
    return NextResponse.next()
  }

  // Public assets'lere erişime izin ver
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.includes('.')
  ) {
    return NextResponse.next()
  }

  // Diğer tüm routes için devam et
  // Authentication kontrolü client-side Frappe React SDK tarafından yapılacak
  return NextResponse.next()
}

export const config = {
  matcher: [
    // Tüm routes'ları kontrol et ama sadece public ones'ları filtrele
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ]
}
