import { FrappeApp } from 'frappe-js-sdk';

// Environment-based URL configuration
const getFrappeURL = () => {
  if (typeof window !== 'undefined') {
    // Client-side
    const hostname = window.location.hostname;
    const port = process.env.NODE_ENV === 'production' ? '' : ':8000';
    const protocol = window.location.protocol;
    return `${protocol}//${hostname}${port}`;
  } else {
    // Server-side
    return process.env.FRAPPE_URL || 'http://localhost:8000';
  }
};

// Create Frappe instance with fallback URL for SSR
const createFrappeInstance = () => {
  const url = typeof window !== 'undefined' ? getFrappeURL() : 'http://localhost:8000';
  return new FrappeApp(url);
};

const frappe = createFrappeInstance();

// Helper functions for common operations
export const frappeUtils = {
  // Get current user info
  getCurrentUser: async () => {
    try {
      // Use the correct Frappe SDK API
      const response = await frappe.call().get('frappe.auth.get_logged_user');
      return response?.message || null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  },

  // Check if user is authenticated (simplified for now)
  isAuthenticated: () => {
    try {
      // This will be managed by the React SDK hooks
      return true; // Will be overridden by useFrappeAuth hook
    } catch (error) {
      return false;
    }
  },

  // Get session info (simplified for now)
  getSessionInfo: () => {
    try {
      return {
        isLoggedIn: false, // Will be managed by React hooks
        user: null
      };
    } catch (error) {
      return {
        isLoggedIn: false,
        user: null
      };
    }
  },

  // Logout from Frappe
  logout: async () => {
    try {
      await frappe.auth().logout();
      return true;
    } catch (error) {
      console.error('Error logging out:', error);
      return false;
    }
  }
};

export { frappe, getFrappeURL };