// Frappe configuration for React SDK
export const FRAPPE_CONFIG = {
  url: process.env.FRAPPE_URL || 'http://localhost:8000',
  socketPort: '9000'
};

// Helper function to get Frappe URL
export const getFrappeURL = () => {
  if (typeof window !== 'undefined') {
    // Client-side
    const hostname = window.location.hostname;
    const port = process.env.NODE_ENV === 'production' ? '' : ':8000';
    const protocol = window.location.protocol;
    return `${protocol}//${hostname}${port}`;
  } else {
    // Server-side
    return FRAPPE_CONFIG.url;
  }
};