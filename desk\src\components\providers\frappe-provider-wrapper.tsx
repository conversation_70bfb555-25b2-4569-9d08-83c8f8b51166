"use client";

import { <PERSON>actNode, useEffect } from "react";
import { useSession } from "next-auth/react";
import { FrappeProvider, useFrappeAuth } from "frappe-react-sdk";
import { getFrappeURL } from "@/lib/frappe/frappeClient";

interface FrappeProviderWrapperProps {
  children: ReactNode;
}

// Inner component that handles the authentication sync
function FrappeAuthSync({ children }: { children: ReactNode }) {
  const { data: session, status } = useSession();
  const { currentUser, updateCurrentUser, logout } = useFrappeAuth();

  useEffect(() => {
    const syncAuth = async () => {
      if (status === "authenticated" && session?.sid) {
        // If NextAuth is authenticated, ensure Frappe is also authenticated
        if (!currentUser) {
          try {
            await updateCurrentUser();
          } catch (error) {
            console.error("Failed to sync Frappe authentication:", error);
          }
        }
      } else if (status === "unauthenticated") {
        // If NextAuth is not authenticated, logout from Frappe
        if (currentUser) {
          try {
            await logout();
          } catch (error) {
            console.error("Failed to logout from Frappe:", error);
          }
        }
      }
    };

    syncAuth();
  }, [status, session, currentUser, updateCurrentUser, logout]);

  return <>{children}</>;
}

// Main wrapper component
export function FrappeProviderWrapper({ children }: FrappeProviderWrapperProps) {
  const frappeUrl = getFrappeURL();
  
  return (
    <FrappeProvider
      url={frappeUrl}
      socketPort="9000"
      enableSocket={true}
    >
      <FrappeAuthSync>
        {children}
      </FrappeAuthSync>
    </FrappeProvider>
  );
}
