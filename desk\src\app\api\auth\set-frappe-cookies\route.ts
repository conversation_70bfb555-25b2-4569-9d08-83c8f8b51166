import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();
    
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { frappeCookies } = await request.json();
    
    if (!frappeCookies) {
      return NextResponse.json({ error: 'No cookies provided' }, { status: 400 });
    }

    // Cookie string'ini parse et
    const cookies = frappeCookies.split(',').map((cookie: string) => cookie.trim());
    
    const response = NextResponse.json({ success: true });
    
    // Her cookie'yi response'a ekle
    cookies.forEach((cookieString: string) => {
      if (cookieString) {
        // Cookie string'ini parse et
        const [nameValue, ...attributes] = cookieString.split(';');
        const [name, value] = nameValue.split('=');
        
        if (name && value) {
          // Cookie'yi response'a ekle
          response.cookies.set(name.trim(), value.trim(), {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            path: '/',
            // Frappe cookie'lerinin özelliklerini koru
            maxAge: 60 * 60 * 24 * 7 // 7 gün
          });
        }
      }
    });

    return response;
  } catch (error) {
    console.error('Error setting Frappe cookies:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
