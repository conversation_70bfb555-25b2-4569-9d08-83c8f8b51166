# IoT Gateway Management System

A modern Next.js application integrated with Frappe backend for IoT device management.

## Features

- 🔐 **Authentication**: NextAuth.js integration with Frappe backend
- 🍪 **Cookie-based Sessions**: Seamless session management between Next.js and Frappe
- 📊 **Real-time Data**: Frappe React SDK for live data synchronization
- 🎨 **Modern UI**: Tailwind CSS with Radix UI components
- 🔄 **State Management**: SWR for efficient data fetching and caching
- 🚀 **TypeScript**: Full type safety throughout the application

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Backend**: Frappe Framework
- **Authentication**: NextAuth.js
- **UI**: Tailwind CSS, Radix UI, Lucide Icons
- **Data Fetching**: Frappe React SDK, SWR
- **Notifications**: Sonner

## Prerequisites

- Node.js 18+
- Frappe Framework running on `http://localhost:8000`
- npm/yarn/pnpm

## Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd desk
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env.local
   ```

   Update `.env.local` with your configuration:
   ```env
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-secret-key-here
   FRAPPE_URL=http://localhost:8000
   NODE_ENV=development
   ```

4. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/auth/          # NextAuth.js API routes
│   ├── dashboard/         # Dashboard pages
│   ├── login/            # Login page
│   └── providers.tsx     # Global providers
├── components/           # React components
│   ├── doctype/         # Frappe DocType components
│   ├── form/            # Form components
│   ├── layout/          # Layout components
│   ├── providers/       # Provider components
│   └── ui/              # UI components
├── hooks/               # Custom React hooks
│   └── use-frappe.ts   # Frappe integration hooks
└── lib/                # Utility libraries
    ├── frappe/         # Frappe client configuration
    └── utils.ts        # General utilities
```

## Authentication Flow

1. User enters credentials on login page
2. NextAuth.js validates credentials with Frappe backend
3. Successful authentication creates both NextAuth and Frappe sessions
4. Session cookies are synchronized between Next.js and Frappe
5. Protected routes automatically redirect unauthenticated users

## Frappe Integration

### Custom Hooks

- `useFrappeWithAuth()`: Authentication state management
- `useFrappeDoc()`: Single document operations
- `useFrappeDocList()`: Document list operations
- `useFrappeCreate()`: Document creation
- `useFrappeUpdate()`: Document updates
- `useFrappeDelete()`: Document deletion
- `useFrappeCall()`: Custom API calls

### Example Usage

```tsx
import { useFrappeDocList, useFrappeCreate } from '@/hooks/use-frappe';

function UserList() {
  const { docs: users, isLoading } = useFrappeDocList("User", {
    fields: ["name", "full_name", "email"],
    filters: { enabled: 1 }
  });

  const { createDoc } = useFrappeCreate();

  const handleCreateUser = async (userData) => {
    await createDoc("User", userData);
  };

  // Component JSX...
}
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Configuration

### Frappe Backend Setup

Ensure your Frappe instance is configured to allow CORS requests from your Next.js application:

```python
# In your Frappe site's site_config.json
{
  "allow_cors": "*",
  "cors_headers": [
    "Authorization",
    "Content-Type",
    "X-Frappe-CSRF-Token"
  ]
}
```

### NextAuth Configuration

The NextAuth configuration is located in `src/app/api/auth/[...nextauth]/route.ts`. It includes:

- Credentials provider for Frappe authentication
- Session and JWT callbacks for user data management
- Cookie configuration for secure session handling

## Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Set production environment variables**
   ```env
   NEXTAUTH_URL=https://your-domain.com
   NEXTAUTH_SECRET=your-production-secret
   FRAPPE_URL=https://your-frappe-instance.com
   NODE_ENV=production
   ```

3. **Deploy to your preferred platform** (Vercel, Netlify, etc.)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
