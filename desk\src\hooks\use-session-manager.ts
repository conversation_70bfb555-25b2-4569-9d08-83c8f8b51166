"use client";

import { useEffect, useRef, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useFrappeAuth, useFrappePostCall } from "frappe-react-sdk";
import { toast } from "sonner";

interface SessionConfig {
  inactivityTimeout?: number; // ms - default 5 minutes
  sessionCheckInterval?: number; // ms - default 30 seconds
  warningBeforeLogout?: number; // ms - default 1 minute
}

export function useSessionManager(config: SessionConfig = {}) {
  const router = useRouter();
  const { currentUser, logout } = useFrappeAuth();
  const { call } = useFrappePostCall();

  const {
    inactivityTimeout = 5 * 60 * 1000, // 5 minutes
    sessionCheckInterval = 30 * 1000, // 30 seconds
    warningBeforeLogout = 60 * 1000 // 1 minute
  } = config;

  const lastActivityRef = useRef<number>(Date.now());
  const sessionCheckRef = useRef<NodeJS.Timeout>();
  const warningTimeoutRef = useRef<NodeJS.Timeout>();
  const logoutTimeoutRef = useRef<NodeJS.Timeout>();

  // Activity tracker
  const updateActivity = useCallback(() => {
    lastActivityRef.current = Date.now();

    // Clear existing warning/logout timers
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
    }
    if (logoutTimeoutRef.current) {
      clearTimeout(logoutTimeoutRef.current);
    }

    // Set new warning timer
    warningTimeoutRef.current = setTimeout(() => {
      if (currentUser) {
        toast.warning("Session expiring soon", {
          description: "You will be logged out in 1 minute due to inactivity",
          duration: 10000
        });
      }
    }, inactivityTimeout - warningBeforeLogout);

    // Set new logout timer
    logoutTimeoutRef.current = setTimeout(() => {
      if (currentUser) {
        handleAutoLogout("inactivity");
      }
    }, inactivityTimeout);
  }, [currentUser, inactivityTimeout, warningBeforeLogout]);

  // Auto logout handler
  const handleAutoLogout = useCallback(async (reason: "inactivity" | "session_expired" | "server_error") => {
    console.log("Auto logout triggered:", reason);

    try {
      await logout();
    } catch (error) {
      console.error("Logout error:", error);
    }

    // Clear all timers
    if (sessionCheckRef.current) clearInterval(sessionCheckRef.current);
    if (warningTimeoutRef.current) clearTimeout(warningTimeoutRef.current);
    if (logoutTimeoutRef.current) clearTimeout(logoutTimeoutRef.current);

    // Show appropriate message
    const messages = {
      inactivity: "You have been logged out due to inactivity",
      session_expired: "Your session has expired",
      server_error: "Session error - please login again"
    };

    toast.error("Session ended", {
      description: messages[reason],
      duration: 5000
    });

    // Redirect to login
    router.replace("/login");
  }, [logout, router]);

  // Session validation - cookie-based
  const validateSession = useCallback(async () => {
    if (!currentUser) return false;

    try {
      // Check cookies instead of API call
      if (typeof window !== 'undefined') {
        const cookies = document.cookie.split(';').reduce((acc, cookie) => {
          const [key, value] = cookie.trim().split('=');
          acc[key] = decodeURIComponent(value || '');
          return acc;
        }, {} as Record<string, string>);

        const sid = cookies.sid;
        const userId = cookies.user_id;

        // Check if essential cookies exist and are not Guest
        if (!sid || !userId || userId === 'Guest' || sid === 'Guest') {
          console.log("Session validation failed: Invalid cookies");
          handleAutoLogout("session_expired");
          return false;
        }

        // Check if currentUser matches cookie
        if (userId !== currentUser) {
          console.log("Session validation failed: User mismatch");
          handleAutoLogout("session_expired");
          return false;
        }

        return true;
      }

      return false;
    } catch (error) {
      console.error("Session validation error:", error);
      handleAutoLogout("server_error");
      return false;
    }
  }, [currentUser, handleAutoLogout]);

  // Setup activity listeners
  useEffect(() => {
    if (!currentUser) return;

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

    const activityHandler = () => {
      updateActivity();
    };

    // Add event listeners
    events.forEach(event => {
      document.addEventListener(event, activityHandler, true);
    });

    // Initial activity update
    updateActivity();

    // Setup session validation interval
    sessionCheckRef.current = setInterval(validateSession, sessionCheckInterval);

    return () => {
      // Cleanup
      events.forEach(event => {
        document.removeEventListener(event, activityHandler, true);
      });

      if (sessionCheckRef.current) clearInterval(sessionCheckRef.current);
      if (warningTimeoutRef.current) clearTimeout(warningTimeoutRef.current);
      if (logoutTimeoutRef.current) clearTimeout(logoutTimeoutRef.current);
    };
  }, [currentUser, updateActivity, validateSession, sessionCheckInterval]);

  return {
    updateActivity,
    validateSession,
    handleAutoLogout
  };
}
