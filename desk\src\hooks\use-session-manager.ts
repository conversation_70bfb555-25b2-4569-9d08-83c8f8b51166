"use client";

import { useEffect, useRef, useCallback } from "react";
import { useRouter } from "next/navigation";
import { useFrappeAuth, useFrappePostCall } from "frappe-react-sdk";
import { toast } from "sonner";

interface SessionConfig {
  inactivityTimeout?: number; // ms - default 5 minutes
  sessionCheckInterval?: number; // ms - default 30 seconds
  warningBeforeLogout?: number; // ms - default 1 minute
}

export function useSessionManager(config: SessionConfig = {}) {
  const router = useRouter();
  const { currentUser, logout } = useFrappeAuth();
  const { call } = useFrappePostCall();
  
  const {
    inactivityTimeout = 5 * 60 * 1000, // 5 minutes
    sessionCheckInterval = 30 * 1000, // 30 seconds
    warningBeforeLogout = 60 * 1000 // 1 minute
  } = config;

  const lastActivityRef = useRef<number>(Date.now());
  const sessionCheckRef = useRef<NodeJS.Timeout>();
  const warningTimeoutRef = useRef<NodeJS.Timeout>();
  const logoutTimeoutRef = useRef<NodeJS.Timeout>();

  // Activity tracker
  const updateActivity = useCallback(() => {
    lastActivityRef.current = Date.now();
    
    // Clear existing warning/logout timers
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
    }
    if (logoutTimeoutRef.current) {
      clearTimeout(logoutTimeoutRef.current);
    }

    // Set new warning timer
    warningTimeoutRef.current = setTimeout(() => {
      if (currentUser) {
        toast.warning("Session expiring soon", {
          description: "You will be logged out in 1 minute due to inactivity",
          duration: 10000
        });
      }
    }, inactivityTimeout - warningBeforeLogout);

    // Set new logout timer
    logoutTimeoutRef.current = setTimeout(() => {
      if (currentUser) {
        handleAutoLogout("inactivity");
      }
    }, inactivityTimeout);
  }, [currentUser, inactivityTimeout, warningBeforeLogout]);

  // Auto logout handler
  const handleAutoLogout = useCallback(async (reason: "inactivity" | "session_expired" | "server_error") => {
    console.log("Auto logout triggered:", reason);
    
    try {
      await logout();
    } catch (error) {
      console.error("Logout error:", error);
    }

    // Clear all timers
    if (sessionCheckRef.current) clearInterval(sessionCheckRef.current);
    if (warningTimeoutRef.current) clearTimeout(warningTimeoutRef.current);
    if (logoutTimeoutRef.current) clearTimeout(logoutTimeoutRef.current);

    // Show appropriate message
    const messages = {
      inactivity: "You have been logged out due to inactivity",
      session_expired: "Your session has expired",
      server_error: "Session error - please login again"
    };

    toast.error("Session ended", {
      description: messages[reason],
      duration: 5000
    });

    // Redirect to login
    router.replace("/login");
  }, [logout, router]);

  // Session validation
  const validateSession = useCallback(async () => {
    if (!currentUser) return;

    try {
      // Check if session is still valid on server
      const response = await call('frappe.auth.get_logged_user');
      
      if (!response || response.message === "Guest") {
        handleAutoLogout("session_expired");
        return false;
      }
      
      return true;
    } catch (error) {
      console.error("Session validation error:", error);
      handleAutoLogout("server_error");
      return false;
    }
  }, [currentUser, call, handleAutoLogout]);

  // Setup activity listeners
  useEffect(() => {
    if (!currentUser) return;

    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const activityHandler = () => {
      updateActivity();
    };

    // Add event listeners
    events.forEach(event => {
      document.addEventListener(event, activityHandler, true);
    });

    // Initial activity update
    updateActivity();

    // Setup session validation interval
    sessionCheckRef.current = setInterval(validateSession, sessionCheckInterval);

    return () => {
      // Cleanup
      events.forEach(event => {
        document.removeEventListener(event, activityHandler, true);
      });
      
      if (sessionCheckRef.current) clearInterval(sessionCheckRef.current);
      if (warningTimeoutRef.current) clearTimeout(warningTimeoutRef.current);
      if (logoutTimeoutRef.current) clearTimeout(logoutTimeoutRef.current);
    };
  }, [currentUser, updateActivity, validateSession, sessionCheckInterval]);

  return {
    updateActivity,
    validateSession,
    handleAutoLogout
  };
}
